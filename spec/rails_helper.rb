# This file is copied to spec/ when you run 'rails generate rspec:install'
require "spec_helper"
ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?
# Uncomment the line below in case you have `--require rails_helper` in the `.rspec` file
# that will avoid rails generators crashing because migrations haven't been run yet
# return unless Rails.env.test?
require "rspec/rails"

# Add additional requires below this line. Rails is not loaded until this point!
require "database_cleaner/active_record"

# Capybara JS driver setup
require "capybara/rspec"

Capybara.register_driver :logging_headless do |app|
  options = Selenium::WebDriver::Chrome::Options.new
  options.add_option("goog:loggingPrefs", {browser: "ALL", driver: "ALL"})
  Capybara::Selenium::Driver.new(app, options: options, browser: :headless_chrome)
end

Capybara.javascript_driver = :logging_headless
Selenium::WebDriver::Chrome::Service.driver_path = "/usr/bin/chromedriver"
Capybara.app = Rails.application

# Ensure Puma is used in threaded mode for JS specs (required for DB visibility)
Capybara.server = :puma, {Silent: true}

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#

Dir[Rails.root.join("spec", "support", "**", "*.rb")].sort.each { |f| require f }

RSpec.configure do |config|
  config.include CapybaraSignInHelper, type: :feature
  config.include CapybaraSignInHelper, type: :system

  # Only include Warden test helpers for non-feature specs
  config.include Warden::Test::Helpers, type: :request
  config.include Warden::Test::Helpers, type: :controller
  # Remove or comment out any global Warden includes
end

# Ensures that the test database schema matches the current schema file.
# If there are pending migrations it will invoke `db:test:prepare` to
# recreate the test database by loading the schema.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end

RSpec.configure do |config|
  # Ensure consistent time for all specs to avoid sold_at future errors
  require "timecop"
  fixed_time = Time.zone.local(2023, 1, 1, 12, 0, 0)
  config.around(:each, type: :model) do |example|
    Timecop.freeze(fixed_time) { example.run }
  end
  # Timecop freeze removed for controller specs due to Rails 8 argument error
  config.around(:each, type: :request) do |example|
    Timecop.freeze(fixed_time) { example.run }
  end

  # Use DatabaseCleaner instead of transactional fixtures
  config.use_transactional_fixtures = false

  config.before(:suite) do
    DatabaseCleaner.clean_with(:truncation)
  end

  config.before(:each) do |example|
    DatabaseCleaner.strategy = if example.metadata[:type] == :request || example.metadata[:js]
      :truncation
    else
      :transaction
    end
    DatabaseCleaner.start
  end

  config.append_after(:each) do
    DatabaseCleaner.clean
  end

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails uses metadata to mix in different behaviours to your tests,
  # for example enabling you to call `get` and `post` in request specs. e.g.:
  #
  #     RSpec.describe UsersController, type: :request do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://rspec.info/features/8-0/rspec-rails
  #
  # You can also this infer these behaviours automatically by location, e.g.
  # /spec/models would pull in the same behaviour as `type: :model` but this
  # behaviour is considered legacy and will be removed in a future version.
  #
  # To enable this behaviour uncomment the line below.
  # config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")
end
